import 'dart:ui';
import 'package:intl/intl.dart';
import '../../enums/broker_enums.dart';

final voucherStatusList = VoucherStatus.allValues;
const lightAccentBlue = Color(0xFF92E2FF);
const lightOrange = Color(0xFFFFD099);
const darkOrange = Color(0xFFFE9B48);

const deliveryModeList = [
  "By Air",
  "By Road",
];

const invoiceStatusList = [
  "In Progress",
  "Completed",
];

String formatDate(DateTime date) {
  // Format the day, month, and year with padding for single-digit numbers
  String day = date.day.toString().padLeft(2, '0');
  String month = date.month.toString().padLeft(2, '0');
  String year = date.year.toString();

  // Combine into the desired format
  return '$day/$month/$year';
}

/// Currency formatting utilities for Pakistani Rupee (PKR)
class CurrencyFormatter {
  // Standard PKR symbol
  static const String pkrSymbol = 'PKR ';
  static const String pkrSymbolShort = '₨ ';

  /// Format currency with full PKR symbol and proper formatting
  static String formatCurrency(double amount, {bool useShortSymbol = false}) {
    final symbol = useShortSymbol ? pkrSymbolShort : pkrSymbol;
    final formatter = NumberFormat.currency(symbol: symbol, decimalDigits: 2);
    return formatter.format(amount);
  }

  /// Format currency with commas and 2 decimal places (no symbol)
  static String formatCurrencyPlain(double amount) {
    if (amount == 0) return '0.00';

    final formatted = amount.toStringAsFixed(2);
    final parts = formatted.split('.');
    final integerPart = parts[0];
    final decimalPart = parts[1];

    // Add commas to integer part
    final regex = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
    final integerWithCommas = integerPart.replaceAllMapped(
      regex,
      (Match match) => '${match[1]},',
    );

    return '$integerWithCommas.$decimalPart';
  }

  /// Format currency with abbreviated values (K, M) for large amounts
  static String formatCurrencyAbbreviated(double amount,
      {bool useShortSymbol = false}) {
    final symbol = useShortSymbol ? pkrSymbolShort : pkrSymbol;
    final isNegative = amount < 0;
    final absAmount = amount.abs();

    String formatted;
    if (absAmount >= 1000000) {
      formatted = '$symbol${(absAmount / 1000000).toStringAsFixed(2)}M';
    } else if (absAmount >= 1000) {
      formatted = '$symbol${(absAmount / 1000).toStringAsFixed(1)}K';
    } else {
      formatted = '$symbol${absAmount.toStringAsFixed(2)}';
    }

    return isNegative ? '($formatted)' : formatted;
  }

  /// Format currency for asset management (no symbol, abbreviated)
  static String formatCurrencyAsset(double amount) {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}M';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return amount.toStringAsFixed(0);
    }
  }
}

extension StringCapitalization on String {
  String capitalizeFirstLetter() {
    if (isEmpty) return toLowerCase();
    return this[0].toUpperCase() + substring(1).toLowerCase();
  }
}
