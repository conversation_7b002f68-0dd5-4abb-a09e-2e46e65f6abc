import 'dart:ui';
import 'package:intl/intl.dart';
import '../../enums/broker_enums.dart';

final voucherStatusList = VoucherStatus.allValues;
const lightAccentBlue = Color(0xFF92E2FF);
const lightOrange = Color(0xFFFFD099);
const darkOrange = Color(0xFFFE9B48);

const deliveryModeList = [
  "By Air",
  "By Road",
];

const invoiceStatusList = [
  "In Progress",
  "Completed",
];

String formatDate(DateTime date) {
  // Format the day, month, and year with padding for single-digit numbers
  String day = date.day.toString().padLeft(2, '0');
  String month = date.month.toString().padLeft(2, '0');
  String year = date.year.toString();

  // Combine into the desired format
  return '$day/$month/$year';
}

/// Monetary rounding utilities for financial calculations
class MonetaryRounding {
  /// Round monetary amount using "round half up" rule
  /// - If decimal portion < 0.50, round down to nearest whole number
  /// - If decimal portion >= 0.50, round up to nearest whole number
  static double roundHalfUp(double amount) {
    if (amount.isNaN || amount.isInfinite) return 0.0;

    final isNegative = amount < 0;
    final absAmount = amount.abs();

    // Get the decimal portion
    final decimalPortion = absAmount - absAmount.floor();

    double rounded;
    if (decimalPortion < 0.5) {
      // Round down
      rounded = absAmount.floorToDouble();
    } else {
      // Round up (>= 0.5)
      rounded = absAmount.ceilToDouble();
    }

    return isNegative ? -rounded : rounded;
  }

  /// Round monetary amount and return as integer
  static int roundHalfUpToInt(double amount) {
    return roundHalfUp(amount).toInt();
  }

  /// Apply rounding to all amounts in a financial calculation
  static Map<String, double> roundFinancialAmounts(
      Map<String, double> amounts) {
    final rounded = <String, double>{};
    for (final entry in amounts.entries) {
      rounded[entry.key] = roundHalfUp(entry.value);
    }
    return rounded;
  }
}

/// Currency formatting utilities for Pakistani Rupee (PKR)
class CurrencyFormatter {
  // Standard PKR symbol
  static const String pkrSymbol = 'PKR ';
  static const String pkrSymbolShort = '₨ ';

  /// Format currency with full PKR symbol and proper formatting
  /// Automatically applies monetary rounding before formatting
  static String formatCurrency(double amount,
      {bool useShortSymbol = false, bool applyRounding = true}) {
    final roundedAmount =
        applyRounding ? MonetaryRounding.roundHalfUp(amount) : amount;
    final symbol = useShortSymbol ? pkrSymbolShort : pkrSymbol;
    final formatter = NumberFormat.currency(symbol: symbol, decimalDigits: 0);
    return formatter.format(roundedAmount);
  }

  /// Format currency with commas and no decimal places (no symbol)
  /// Automatically applies monetary rounding before formatting
  static String formatCurrencyPlain(double amount,
      {bool applyRounding = true}) {
    final roundedAmount =
        applyRounding ? MonetaryRounding.roundHalfUp(amount) : amount;
    if (roundedAmount == 0) return '0';

    final formatted = roundedAmount.toStringAsFixed(0);

    // Add commas to integer part
    final regex = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
    final integerWithCommas = formatted.replaceAllMapped(
      regex,
      (Match match) => '${match[1]},',
    );

    return integerWithCommas;
  }

  /// Format currency with abbreviated values (K, M) for large amounts
  /// Automatically applies monetary rounding before formatting
  static String formatCurrencyAbbreviated(double amount,
      {bool useShortSymbol = false, bool applyRounding = true}) {
    final roundedAmount =
        applyRounding ? MonetaryRounding.roundHalfUp(amount) : amount;
    final symbol = useShortSymbol ? pkrSymbolShort : pkrSymbol;
    final isNegative = roundedAmount < 0;
    final absAmount = roundedAmount.abs();

    String formatted;
    if (absAmount >= 1000000) {
      formatted = '$symbol${(absAmount / 1000000).toStringAsFixed(1)}M';
    } else if (absAmount >= 1000) {
      formatted = '$symbol${(absAmount / 1000).toStringAsFixed(1)}K';
    } else {
      formatted = '$symbol${absAmount.toStringAsFixed(0)}';
    }

    return isNegative ? '($formatted)' : formatted;
  }

  /// Format currency for asset management (no symbol, abbreviated)
  /// Automatically applies monetary rounding before formatting
  static String formatCurrencyAsset(double amount,
      {bool applyRounding = true}) {
    final roundedAmount =
        applyRounding ? MonetaryRounding.roundHalfUp(amount) : amount;
    final absAmount = roundedAmount.abs();

    if (absAmount >= 1000000) {
      return '${(absAmount / 1000000).toStringAsFixed(1)}M';
    } else if (absAmount >= 1000) {
      return '${(absAmount / 1000).toStringAsFixed(1)}K';
    } else {
      return absAmount.toStringAsFixed(0);
    }
  }
}

extension StringCapitalization on String {
  String capitalizeFirstLetter() {
    if (isEmpty) return toLowerCase();
    return this[0].toUpperCase() + substring(1).toLowerCase();
  }
}
