import 'dart:developer';
import 'package:excel/excel.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logestics/models/invoice_model.dart';
import 'package:logestics/models/finance/bill_model.dart';
import 'package:logestics/models/slab/slab_model.dart';
import 'package:logestics/firebase_service/finance/bill_firebase_service.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/services/slab_rate_calculation_service.dart';
import 'package:logestics/features/slab/domain/usecases/get_slabs_use_case.dart';
import 'package:universal_html/html.dart' as html;
import 'package:uuid/uuid.dart';
import '../../../company/presentation/conrollers/company_controller.dart';

class BillingExcelExportController extends GetxController {
  final CompanyController companyController = Get.find<CompanyController>();
  final BillFirebaseService _billFirebaseService = BillFirebaseService();
  final SlabRateCalculationService _slabRateCalculationService =
      Get.find<SlabRateCalculationService>();
  final GetSlabsUseCase _getSlabsUseCase = Get.find<GetSlabsUseCase>();

  // Date range
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);

  // Slab selection
  final RxList<SlabModel> availableSlabs = <SlabModel>[].obs;
  final Rx<SlabModel?> selectedSlab = Rx<SlabModel?>(null);

  // Tax authority selection
  final RxList<String> selectedTaxAuthorities = <String>[].obs;
  final List<String> availableTaxAuthorities = ['SRB', 'PRA', 'BRA', 'KRA'];
  final RxString taxAuthorityError = ''.obs;

  // Filtered invoices
  final RxList<InvoiceModel> filteredInvoices = <InvoiceModel>[].obs;

  // Loading states
  final RxBool isLoading = false.obs;
  final RxBool isSavingBill = false.obs;
  final RxBool isLoadingSlabs = false.obs;

  @override
  void onInit() {
    super.onInit();

    // Set default date range (last 30 days)
    final now = DateTime.now();
    startDate.value = DateTime(now.year, now.month - 1, now.day);
    endDate.value = now;

    // Listen to date changes and filter invoices
    ever(startDate, (_) => _filterInvoices());
    ever(endDate, (_) => _filterInvoices());

    // Load all active slabs
    loadAllActiveSlabs();

    // Initial filter
    _filterInvoices();
  }

  /// Load all active slabs from all districts
  Future<void> loadAllActiveSlabs() async {
    try {
      isLoadingSlabs.value = true;

      // Use current date for slab validity check
      final currentDate = DateTime.now();

      final result = await _getSlabsUseCase.call();

      result.fold(
        (failure) {
          log('Error loading slabs: ${failure.message}');
          availableSlabs.clear();
        },
        (allSlabs) {
          // Filter to only active slabs (valid for current date)
          final activeSlabs = allSlabs.where((slab) {
            return slab.isValidForDate(currentDate);
          }).toList();

          availableSlabs.value = activeSlabs;
          log('Successfully loaded ${activeSlabs.length} active slabs from ${allSlabs.length} total slabs');
        },
      );
    } catch (e) {
      log('Unexpected error loading slabs: $e');
      availableSlabs.clear();
    } finally {
      isLoadingSlabs.value = false;
    }
  }

  /// Handle slab selection change
  void onSlabSelected(String? slabDisplayName) {
    if (slabDisplayName == null) {
      selectedSlab.value = null;
      return;
    }

    // Extract slab name from display format "Slab Name - District Name"
    final slabName = slabDisplayName.split(' - ').first;

    final slab = availableSlabs.firstWhereOrNull(
      (s) => s.slabName == slabName,
    );

    if (slab != null) {
      selectedSlab.value = slab;
      log('Selected slab: ${slab.slabName}');
    }
  }

  /// Get display name for slab (includes district names)
  String getSlabDisplayName(SlabModel slab) {
    // Get all unique district names from this slab's rates
    final districtNames = slab.rates
        .map((rate) => rate.districtName)
        .where((name) => name.isNotEmpty)
        .toSet()
        .toList();

    if (districtNames.isEmpty) {
      return slab.slabName;
    }

    // Sort district names for consistent display
    districtNames.sort();

    // If only one district, show "Slab Name - District Name"
    if (districtNames.length == 1) {
      return '${slab.slabName} - ${districtNames.first}';
    }

    // If multiple districts, show "Slab Name - Multiple Districts"
    return '${slab.slabName} - Multiple Districts (${districtNames.length})';
  }

  void setDateRange(DateTime start, DateTime end) {
    // Validate date range
    if (end.isBefore(start)) {
      log('End date cannot be before start date');
      return;
    }

    startDate.value = start;
    endDate.value = end;
  }

  void _filterInvoices() {
    if (startDate.value == null || endDate.value == null) {
      filteredInvoices.clear();
      return;
    }

    final start = startDate.value!;
    final end = endDate.value!;

    // Filter invoices by date range and only include "Received" status
    final filtered = companyController.company.invoices.where((invoice) {
      // Only include invoices with "Received" status - exclude all other statuses
      // This ensures only invoices eligible for billing are shown
      if (invoice.invoiceStatus != 'Received') {
        return false;
      }

      // Check date range (use belongsToDate, fallback to orderDate, then createdAt)
      final invoiceDate =
          invoice.belongsToDate ?? invoice.orderDate ?? invoice.createdAt;

      // Check if invoice date is within the selected range (inclusive)
      return invoiceDate.isAfter(start.subtract(const Duration(days: 1))) &&
          invoiceDate.isBefore(end.add(const Duration(days: 1)));
    }).toList();

    filteredInvoices.value = filtered;

    // Log filtering results for debugging
    final totalInvoices = companyController.company.invoices.length;
    final receivedInvoices = companyController.company.invoices
        .where((invoice) => invoice.invoiceStatus == 'Received')
        .length;
    final pendingBillingInvoices = companyController.company.invoices
        .where((invoice) => invoice.invoiceStatus == 'Pending Billing')
        .length;
    final paymentReceivedInvoices = companyController.company.invoices
        .where((invoice) => invoice.invoiceStatus == 'Payment Received')
        .length;

    log('Invoice filtering results:');
    log('  Total invoices: $totalInvoices');
    log('  Received status: $receivedInvoices');
    log('  Pending Billing status: $pendingBillingInvoices');
    log('  Payment Received status: $paymentReceivedInvoices');
    log('  Filtered for billing (Received + date range): ${filtered.length}');
    log('  Date range: ${DateFormat('dd/MM/yyyy').format(start)} - ${DateFormat('dd/MM/yyyy').format(end)}');
  }

  Future<void> generateExcel() async {
    if (filteredInvoices.isEmpty) {
      log('No invoices to export');
      return;
    }

    isLoading.value = true;

    try {
      // Generate filename
      final dateFormat = DateFormat('yyyy-MM-dd');
      final fileName =
          'Billing_Report_${dateFormat.format(startDate.value!)}_to_${dateFormat.format(endDate.value!)}.xlsx';

      log('Generating Excel file: $fileName with ${filteredInvoices.length} invoices');

      // Create Excel workbook
      final excel = Excel.createExcel();
      final sheet = excel['Billing Report'];

      // Remove default sheet if it exists
      if (excel.sheets.containsKey('Sheet1')) {
        excel.delete('Sheet1');
      }

      // Add headers - enhanced with slab rate columns (16 columns total)
      final headers = [
        'SN.',
        'Lifting Date', // Changed from 'Order Date'
        'Truck No',
        'Bilty No',
        'Convey Note Number',
        'Product Name', // New column added after Convey Note Number
        'Product TAS NO',
        'Destination',
        'No of Bags',
        'Weight', // Changed from 'Total Weight (Tons)'
        'KM',
        'District',
        'HMT Rates (Non Fuel Inc WHT)',
        '100% Amount',
        '80% Amount',
        'Net Amount',
      ];

      // Style for headers - clean black text only
      final headerStyle = CellStyle(
        bold: true,
        fontColorHex: ExcelColor.black,
      );

      // Add headers to first row
      for (int i = 0; i < headers.length; i++) {
        final cell =
            sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
        cell.value = TextCellValue(headers[i]);
        cell.cellStyle = headerStyle;
      }

      // Calculate slab rates and amounts for all invoices
      double totalTons = 0.0;
      double total100Amount = 0.0;
      double total80Amount = 0.0;

      // Add data rows - enhanced with slab rate calculations (15 columns total)
      for (int i = 0; i < filteredInvoices.length; i++) {
        final invoice = filteredInvoices[i];
        final rowIndex = i + 1;

        // Calculate total weight in tons: (numberOfBags × weightPerBag) ÷ 1000
        final totalWeightTons =
            (invoice.numberOfBags * invoice.weightPerBag) / 1000;

        // Format weight with dynamic decimal places (remove trailing zeros)
        final totalWeightFormatted = totalWeightTons % 1 == 0
            ? totalWeightTons.toInt().toString() // Whole number: "12"
            : totalWeightTons
                .toStringAsFixed(2)
                .replaceAll(RegExp(r'\.?0+$'), ''); // Remove trailing zeros

        // Get HMT rate from selected slab
        double hmtRate = 0.0;
        if (selectedSlab.value != null) {
          final slabRate =
              selectedSlab.value!.getRateForDistrict(invoice.districtId);
          if (slabRate != null) {
            hmtRate = slabRate.nonFuelRate; // Use Non Fuel Rate as specified
          }
        }

        // Calculate amounts
        final amount100 =
            totalWeightTons * hmtRate; // 100% Amount = Total Weight × HMT Rate
        final amount80 = amount100 * 0.80; // 80% Amount = 100% Amount × 0.80
        final netAmount = amount100; // Net Amount = 100% Amount

        // Add to totals
        totalTons += totalWeightTons;
        total100Amount += amount100;
        total80Amount += amount80;

        // Log calculation for debugging (first few invoices only)
        if (i < 3) {
          log('Invoice ${invoice.tasNumber}: ${invoice.numberOfBags} bags × ${invoice.weightPerBag} kg = $totalWeightFormatted tons, HMT Rate: $hmtRate, 100% Amount: ${amount100.toStringAsFixed(2)}');
        }

        final rowData = [
          (i + 1).toString(), // SN. - Sequential row number starting from 1
          invoice.orderDate != null
              ? DateFormat('dd/MM/yyyy').format(invoice.orderDate!)
              : '', // Lifting Date (formerly Order Date)
          invoice.truckNumber, // Truck No
          invoice.biltyNumber, // Bilty No
          invoice.conveyNoteNumber, // Convey Note Number
          invoice.productName, // Product Name - new column
          invoice.tasNumber, // Product TAS NO
          invoice.stationName, // Destination
          invoice.numberOfBags.toString(), // No of Bags
          totalWeightFormatted, // Weight (formerly Total Weight (Tons)) - calculated field
          invoice.distanceInKilometers.toString(), // KM
          invoice.districtName, // District
          hmtRate.toStringAsFixed(2), // HMT Rates (Non Fuel Inc WHT)
          amount100.toStringAsFixed(2), // 100% Amount
          amount80.toStringAsFixed(2), // 80% Amount
          netAmount.toStringAsFixed(2), // Net Amount
        ];

        for (int j = 0; j < rowData.length; j++) {
          final cell = sheet.cell(
              CellIndex.indexByColumnRow(columnIndex: j, rowIndex: rowIndex));
          cell.value = TextCellValue(rowData[j]);
        }
      }

      // Add summary section
      final summaryStartRow =
          filteredInvoices.length + 2; // Leave one empty row

      // Summary header style - clean black text only
      final summaryHeaderStyle = CellStyle(
        bold: true,
        fontColorHex: ExcelColor.black,
      );

      // Summary data style - clean black text only
      final summaryDataStyle = CellStyle(
        bold: true,
        fontColorHex: ExcelColor.black,
      );

      // Add summary section - compact format matching screenshot with tax calculations
      final summaryData = <List<String>>[];

      // Base summary row
      summaryData.add([
        '', // Column 0: SN.
        '', // Column 1: Lifting Date (formerly Order Date)
        '', // Column 2: Truck No
        '', // Column 3: Bilty No
        '', // Column 4: Convey Note Number
        '', // Column 5: Product Name - new column
        '', // Column 6: Product TAS NO
        '', // Column 7: Destination
        '', // Column 8: No of Bags
        'Tons', // Column 9: Weight (formerly Total Weight (Tons)) - "Tons" label
        totalTons.toStringAsFixed(2), // Column 10: KM - Tons value
        'Bill Amount Exclusive of GST (100%)', // Column 11: District - Start of label
        '', // Column 12: HMT Rates - Continue label (label spans columns 11-12)
        total100Amount
            .toStringAsFixed(2), // Column 13: 100% Amount - 100% amount value
        total80Amount
            .toStringAsFixed(2), // Column 14: 80% Amount - 80% amount value
        total100Amount
            .toStringAsFixed(2), // Column 15: Net Amount - Net amount value
      ]);

      // Add tax authority rows if any are selected
      if (selectedTaxAuthorities.isNotEmpty) {
        for (String authority in selectedTaxAuthorities) {
          final taxAmount =
              calculateIndividualTaxAmount(total100Amount, authority);
          summaryData.add([
            '', '', '', '', '', '', '', '', '', '',
            '', // Empty columns 0-10 (added one more for Product Name)
            '$authority Sales Tax (15%)', // Column 11: Tax authority label
            '', // Column 12: Continue label
            '', // Column 13: Empty
            '', // Column 14: Empty
            taxAmount.toStringAsFixed(2), // Column 15: Tax amount
          ]);
        }

        // Add total amount with tax row
        final totalTaxAmount = calculateTaxAmount(total100Amount);
        final totalWithTax = total100Amount + totalTaxAmount;
        summaryData.add([
          '', '', '', '', '', '', '', '', '', '',
          '', // Empty columns 0-10 (added one more for Product Name)
          'Total Amount with Tax', // Column 11: Total with tax label
          '', // Column 12: Continue label
          '', // Column 13: Empty
          '', // Column 14: Empty
          totalWithTax.toStringAsFixed(2), // Column 15: Total with tax amount
        ]);
      }

      for (int i = 0; i < summaryData.length; i++) {
        final rowIndex = summaryStartRow + i;
        for (int j = 0; j < summaryData[i].length; j++) {
          final cell = sheet.cell(
              CellIndex.indexByColumnRow(columnIndex: j, rowIndex: rowIndex));
          cell.value = TextCellValue(summaryData[i][j]);

          // Apply styles to summary labels and values
          if (summaryData[i][j].isNotEmpty) {
            // Apply bold style to labels and values
            if (j == 8 || j == 10) {
              // Labels: "Tons", "Bill Amount Exclusive of GST (100%)", tax authority labels, and "Total Amount with Tax"
              cell.cellStyle = summaryHeaderStyle;
            } else {
              // Values: amounts
              cell.cellStyle = summaryDataStyle;
            }
          }
        }
      }

      // Auto-fit columns (approximate)
      for (int i = 0; i < headers.length; i++) {
        sheet.setColumnWidth(i, 15.0);
      }

      // Generate Excel bytes
      final excelBytes = excel.encode();
      if (excelBytes == null) {
        throw Exception('Failed to generate Excel file');
      }

      // Download file for web
      if (kIsWeb) {
        await _downloadExcelWeb(Uint8List.fromList(excelBytes), fileName);
      } else {
        // For mobile/desktop, you would save to file system
        throw UnimplementedError(
            'Mobile/Desktop Excel download not implemented');
      }

      log('Excel file generated successfully: $fileName');

      // Close the dialog after successful generation
      Get.back();

      // Show success message
      Get.snackbar(
        'Success',
        'Excel file "$fileName" has been downloaded',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      log('Error generating Excel file: $e');

      // Show error message
      Get.snackbar(
        'Error',
        'Failed to generate Excel file: $e',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// Download Excel file for web platform
  Future<void> _downloadExcelWeb(Uint8List excelBytes, String fileName) async {
    try {
      final blob = html.Blob([excelBytes],
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      final url = html.Url.createObjectUrlFromBlob(blob);

      html.AnchorElement(href: url)
        ..setAttribute('download', fileName)
        ..click();

      html.Url.revokeObjectUrl(url);

      log('Excel file downloaded successfully on web: $fileName');
    } catch (e) {
      log('Error downloading Excel file on web: $e');
      rethrow;
    }
  }

  /// Save bill and update invoice statuses
  Future<void> saveBill() async {
    if (filteredInvoices.isEmpty) {
      log('No invoices to save as bill');
      Get.snackbar(
        'Error',
        'No invoices selected for billing',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
      return;
    }

    isSavingBill.value = true;

    try {
      final companyUid = companyController.company.uid;

      // Generate unique bill number with race condition protection
      final billNumber =
          await _billFirebaseService.generateUniqueBillNumber(uid: companyUid);

      // Calculate total amount using slab rates
      log('Calculating bill amount using slab rates for ${filteredInvoices.length} invoices');
      final calculationResult =
          await _slabRateCalculationService.calculateBatchInvoiceAmounts(
        invoices: filteredInvoices,
        rateType: 'hmtRate', // Use HMT rate by default
        fallbackRate: 0.1, // Fallback to original rate if no slab rate found
      );

      final totalAmount = calculationResult.totalAmount;

      // Log calculation summary
      log('Bill calculation summary: ${calculationResult.summary}');

      if (!calculationResult.allUsedSlabRates) {
        log('Warning: ${calculationResult.fallbackRateUsedCount} invoices used fallback rate due to missing slab rates');
      }

      // Get invoice IDs (TAS numbers)
      final invoiceIds =
          filteredInvoices.map((invoice) => invoice.tasNumber).toList();

      // Determine primary customer name (if all invoices have same customer)
      final customerNames =
          filteredInvoices.map((invoice) => invoice.customerName).toSet();
      final primaryCustomer =
          customerNames.length == 1 ? customerNames.first : null;

      // Create bill model
      final bill = BillModel(
        billId: const Uuid().v4(),
        billNumber: billNumber,
        billDate: DateTime.now(),
        totalAmount: totalAmount,
        numberOfLinkedInvoices: filteredInvoices.length,
        billStatus: BillStatus.pending,
        linkedInvoiceIds: invoiceIds,
        companyUid: companyUid,
        customerName: primaryCustomer,
        notes:
            'Bill created from billing export for ${filteredInvoices.length} invoices. ${calculationResult.summary}',
        selectedSlabId: selectedSlab.value?.slabId,
        selectedSlabName: selectedSlab.value?.slabName,
        selectedDistrictId: null, // No specific district selected
        selectedDistrictName: null, // No specific district selected
      );

      // Save bill to Firebase (this will also update invoice statuses)
      await _billFirebaseService.createBill(
        uid: companyUid,
        bill: bill,
        invoiceIds: invoiceIds,
      );

      log('Bill $billNumber saved successfully with ${invoiceIds.length} linked invoices');

      // Refresh company data to reflect updated invoice statuses
      companyController.forceRefresh();

      // Close the dialog after successful save
      Get.back();

      // Show success message
      Get.snackbar(
        AppStrings.billCreatedSuccessfully,
        'Bill $billNumber created with ${filteredInvoices.length} invoices',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );

      // Clear filtered invoices since they're now "Pending Billing"
      _filterInvoices();
    } catch (e) {
      log('Error saving bill: $e');

      // Show error message
      Get.snackbar(
        'Error',
        'Failed to save bill: $e',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
    } finally {
      isSavingBill.value = false;
    }
  }

  /// Generate Excel and Save Bill (combined action)
  Future<void> generateExcelAndSaveBill() async {
    if (filteredInvoices.isEmpty) {
      log('No invoices to export and save');
      return;
    }

    // First generate Excel
    await generateExcel();

    // Then save bill (only if Excel generation was successful)
    if (!isLoading.value) {
      await saveBill();
    }
  }

  // Tax authority selection methods
  void toggleTaxAuthority(String authority) {
    if (selectedTaxAuthorities.contains(authority)) {
      selectedTaxAuthorities.remove(authority);
      taxAuthorityError.value = '';
    } else {
      if (selectedTaxAuthorities.length >= 2) {
        taxAuthorityError.value = 'Maximum 2 tax authorities can be selected';
        return;
      }
      selectedTaxAuthorities.add(authority);
      taxAuthorityError.value = '';
    }
  }

  bool isTaxAuthoritySelected(String authority) {
    return selectedTaxAuthorities.contains(authority);
  }

  void clearTaxAuthorityError() {
    taxAuthorityError.value = '';
  }

  // Calculate tax amounts
  double calculateTaxAmount(double totalAmount) {
    if (selectedTaxAuthorities.isEmpty) return 0.0;

    if (selectedTaxAuthorities.length == 1) {
      // Single authority gets full 15% tax
      return totalAmount * 0.15;
    } else {
      // Dual authorities split the amount 50/50, then 15% tax on each
      return (totalAmount * 0.5 * 0.15) * 2; // Same as totalAmount * 0.15
    }
  }

  double calculateIndividualTaxAmount(double totalAmount, String authority) {
    if (!selectedTaxAuthorities.contains(authority)) return 0.0;

    if (selectedTaxAuthorities.length == 1) {
      return totalAmount * 0.15;
    } else {
      // Split amount 50/50, then 15% tax
      return totalAmount * 0.5 * 0.15;
    }
  }
}
